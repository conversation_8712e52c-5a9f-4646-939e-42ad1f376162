import React, { useState } from 'react';
import { <PERSON><PERSON>ail, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>yeOff, FiUser } from 'react-icons/fi';
import { FaGoogle, FaFacebook } from 'react-icons/fa';

const Register = ({ onSuccess }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (formData.password !== formData.confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      onSuccess && onSuccess();
    }, 1500);
  };

  return (
    <div className="signin-form-container">
      <form onSubmit={handleSubmit} className="signin-form">
        <div className="form-group">
          <div className="input-group">
            <div className="input-icon">
              <FiUser size={18} />
            </div>
            <input
              type="text"
              id="firstName"
              name="firstName"
              className="signin-input"
              placeholder="First Name"
              value={formData.firstName}
              onChange={handleChange}
              required
            />
          </div>
        </div>

        <div className="form-group">
          <div className="input-group">
            <div className="input-icon">
              <FiUser size={18} />
            </div>
            <input
              type="text"
              id="lastName"
              name="lastName"
              className="signin-input"
              placeholder="Last Name"
              value={formData.lastName}
              onChange={handleChange}
              required
            />
          </div>
        </div>

        <div className="form-group">
          <div className="input-group">
            <div className="input-icon">
              <FiMail size={18} />
            </div>
            <input
              type="email"
              id="email"
              name="email"
              className="signin-input"
              placeholder="Email"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>
        </div>

        <div className="form-group">
          <div className="input-group">
            <div className="input-icon">
              <FiLock size={18} />
            </div>
            <input
              type={showPassword ? 'text' : 'password'}
              id="password"
              name="password"
              className="signin-input"
              placeholder="Password"
              value={formData.password}
              onChange={handleChange}
              required
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
            </button>
          </div>
        </div>

        <div className="form-group">
          <div className="input-group">
            <div className="input-icon">
              <FiLock size={18} />
            </div>
            <input
              type={showConfirmPassword ? 'text' : 'password'}
              id="confirmPassword"
              name="confirmPassword"
              className="signin-input"
              placeholder="Confirm Password"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
            </button>
          </div>
        </div>

        <button
          type="submit"
          className="signin-submit-btn"
          disabled={isLoading}
        >
          {isLoading ? 'Creating Account...' : 'REGISTER'}
        </button>

        <div className="social-login-section">
          <p className="social-login-text">Or Register with</p>
          <div className="social-buttons">
            <button type="button" className="social-btn google-btn">
              <FaGoogle size={16} />
              <span>Google</span>
            </button>
            <button type="button" className="social-btn facebook-btn">
              <FaFacebook size={16} />
              <span>Facebook</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default Register;
