/* Modern SignIn Layout */
.modern-signin-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Left Panel - Pink Gradient */
.signin-left-panel {
  flex: 1;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 50%, #ffa8a8 100%);
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0;
}

.signin-tabs-container {
  padding: 2rem 2rem 0 2rem;
}

.signin-tabs {
  display: flex;
  gap: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 4px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.signin-tab {
  flex: 1;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  border-radius: 20px;
  background: transparent;
  border: none;
}

.signin-tab:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.signin-tab.active {
  background: white;
  color: #ff6b9d;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 700;
}

/* Right Panel - Form */
.signin-right-panel {
  flex: 1;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.signin-form-wrapper {
  width: 100%;
  max-width: 400px;
}

/* Logo Section */
.signin-logo-section {
  text-align: center;
  margin-bottom: 3rem;
}

.signin-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.logo-squares {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
  width: 50px;
  height: 50px;
}

.square {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
  border-radius: 4px;
  animation: logoFloat 3s ease-in-out infinite;
}

.square-1 { animation-delay: 0s; }
.square-2 { animation-delay: 0.5s; }
.square-3 { animation-delay: 1s; }
.square-4 { animation-delay: 1.5s; }

.signin-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ff6b9d;
  margin: 0;
  letter-spacing: 2px;
}

/* Form Styles */
.signin-form-container {
  width: 100%;
}

.signin-form {
  width: 100%;
}

.signin-form .form-group {
  margin-bottom: 1.5rem;
}

.signin-form .input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.signin-form .input-icon {
  position: absolute;
  left: 1rem;
  color: #a0aec0;
  z-index: 2;
  pointer-events: none;
}

.signin-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #4a5568;
}

.signin-input:focus {
  outline: none;
  border-color: #ff6b9d;
  box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

.signin-input::placeholder {
  color: #a0aec0;
  font-size: 0.9rem;
}

.signin-form .password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  padding: 0;
  z-index: 2;
  transition: color 0.3s ease;
}

.signin-form .password-toggle:hover {
  color: #ff6b9d;
}

.forgot-password-link {
  text-align: right;
  margin-bottom: 2rem;
}

.forgot-password {
  color: #ff6b9d;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #e55a87;
  text-decoration: underline;
}

.signin-submit-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
  border: none;
  border-radius: 25px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  letter-spacing: 1px;
}

.signin-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
}

.signin-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Social Login */
.social-login-section {
  text-align: center;
}

.social-login-text {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.social-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.social-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-btn:hover {
  border-color: #cbd5e0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.google-btn:hover {
  border-color: #db4437;
  color: #db4437;
}

.facebook-btn:hover {
  border-color: #4267b2;
  color: #4267b2;
}

/* Animations */
@keyframes logoFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-3px) scale(1.05); }
}

/* Responsive Design for Modern SignIn */
@media (max-width: 768px) {
  .modern-signin-container {
    flex-direction: column;
  }

  .signin-left-panel {
    flex: none;
    height: auto;
  }

  .signin-tabs-container {
    padding: 1.5rem;
  }

  .signin-tabs {
    max-width: 300px;
    margin: 0 auto;
  }

  .signin-tab {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }

  .signin-right-panel {
    flex: 1;
    padding: 1.5rem;
  }

  .signin-logo-section {
    margin-bottom: 2rem;
  }

  .signin-title {
    font-size: 1.5rem;
  }

  .social-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .social-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Compact Auth Layout with Video Background */
.compact-auth-container {
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Video Background */
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.bg-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  backdrop-filter: blur(2px);
}

/* Centered Content Wrapper */
.auth-content-wrapper {
  position: relative;
  z-index: 10;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* Compact Auth Card */
.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  padding: 2rem;
  width: 100%;
  max-width: 420px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Auth Header */
.auth-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: #f8fafc;
  border-radius: 12px;
  padding: 0.25rem;
  margin-bottom: 1.5rem;
  gap: 0.25rem;
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-radius: 10px;
  color: #718096;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.tab-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tab-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Compact Illustration */
.compact-illustration {
  text-align: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  position: relative;
  overflow: hidden;
}

.illustration-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.illustration-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  line-height: 1.4;
}

/* Mini Graphics for Compact Design */
.mini-graphic {
  position: relative;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mini-envelope {
  width: 50px;
  height: 35px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: miniFloat 3s ease-in-out infinite;
}

.envelope-icon {
  color: rgba(255, 255, 255, 0.9);
}

.floating-dots {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: dotFloat 4s ease-in-out infinite;
}

.dot-1 {
  top: 10px;
  left: 20px;
  animation-delay: 0s;
}

.dot-2 {
  top: 30px;
  right: 25px;
  animation-delay: 1.3s;
}

.dot-3 {
  bottom: 15px;
  left: 30px;
  animation-delay: 2.6s;
}

/* Compact Form Section */
.compact-form-section {
  margin-top: 0;
}

.form-wrapper {
  width: 100%;
  padding: 0;
}

/* Updated Form Styling for Compact Design */
.auth-form-container {
  width: 100%;
}

.auth-form-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.auth-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.auth-subtitle {
  color: #718096;
  font-size: 0.85rem;
  margin: 0;
}

.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: block;
  margin-bottom: 0.4rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.85rem;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 0.875rem;
  color: #a0aec0;
  z-index: 2;
  pointer-events: none;
}

.auth-input {
  width: 100%;
  padding: 0.75rem 0.875rem 0.75rem 2.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: #ffffff;
}

.auth-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.auth-input::placeholder {
  color: #a0aec0;
  font-size: 0.85rem;
}

.password-toggle {
  position: absolute;
  right: 0.875rem;
  background: none;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  padding: 0;
  z-index: 2;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #667eea;
}

/* Form Options and Submit Button */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  font-size: 0.8rem;
}

.form-check {
  display: flex;
  align-items: center;
}

.form-check-input {
  margin-right: 0.4rem;
  accent-color: #667eea;
}

.form-check-label {
  color: #4a5568;
  cursor: pointer;
  font-size: 0.8rem;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  font-size: 0.8rem;
}

.forgot-password:hover {
  color: #5a67d8;
  text-decoration: underline;
}

.auth-submit-btn {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.auth-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.auth-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.auth-footer {
  text-align: center;
  color: #718096;
  font-size: 0.8rem;
}

.auth-link {
  color: #667eea;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #5a67d8;
  text-decoration: underline;
}

/* Success Component for Compact Design */
.success-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  padding: 2rem;
  width: 100%;
  max-width: 420px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.success-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: transparent;
  min-height: auto;
}

.success-content {
  text-align: center;
  color: #2d3748;
  max-width: 100%;
}

.success-icon {
  margin-bottom: 1.5rem;
  color: #48bb78;
  animation: successPulse 2s ease-in-out infinite;
}

.success-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #2d3748;
}

.success-message {
  font-size: 0.95rem;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  color: #718096;
}

.success-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 8px;
  color: #4a5568;
  font-size: 0.85rem;
}

.feature-icon {
  color: #48bb78;
}

.success-btn {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.success-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Animations */
@keyframes miniFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

@keyframes dotFloat {
  0%, 100% { transform: translateY(0px) scale(1); opacity: 0.4; }
  50% { transform: translateY(-8px) scale(1.2); opacity: 0.8; }
}

@keyframes successPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.form-check {
  display: flex;
  align-items: center;
}

.form-check-input {
  margin-right: 0.5rem;
  accent-color: #667eea;
}

.form-check-label {
  color: #4a5568;
  cursor: pointer;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #5a67d8;
  text-decoration: underline;
}

.auth-submit-btn {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.auth-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.auth-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.auth-footer {
  text-align: center;
  color: #718096;
  font-size: 0.9rem;
}

.auth-link {
  color: #667eea;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #5a67d8;
  text-decoration: underline;
}

/* Success Component */
.success-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.success-content {
  text-align: center;
  color: white;
  max-width: 500px;
}

.success-icon {
  margin-bottom: 2rem;
  color: #48bb78;
  animation: successPulse 2s ease-in-out infinite;
}

.success-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.success-message {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.success-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 3rem;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.feature-icon {
  color: #48bb78;
}

.success-btn {
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.success-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

@keyframes successPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .modern-auth-container {
    flex-direction: column;
  }

  .auth-sidebar {
    width: 100%;
    height: auto;
    order: 1;
  }

  .sidebar-content {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
  }

  .nav-tabs {
    flex-direction: row;
    margin-top: 0;
    gap: 0.5rem;
  }

  .nav-tab {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .nav-tab.active,
  .nav-tab:hover {
    transform: none;
  }

  .auth-center {
    order: 2;
    min-height: 300px;
  }

  .illustration-title {
    font-size: 2rem;
  }

  .illustration-subtitle {
    font-size: 1rem;
  }

  .auth-form-section {
    width: 100%;
    order: 3;
  }
}

@media (max-width: 768px) {
  .sidebar-content {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .logo-section {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
  }

  .nav-tabs {
    width: 100%;
    justify-content: center;
  }

  .auth-center {
    min-height: 250px;
    padding: 1rem;
  }

  .illustration-title {
    font-size: 1.75rem;
  }

  .illustration-subtitle {
    font-size: 0.95rem;
    margin-bottom: 2rem;
  }

  .illustration-graphic {
    width: 250px;
    height: 250px;
  }

  .form-wrapper {
    padding: 1.5rem;
  }

  .auth-title {
    font-size: 1.75rem;
  }

  .success-title {
    font-size: 2rem;
  }

  .success-features {
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .form-wrapper {
    padding: 1rem;
  }

  .auth-title {
    font-size: 1.5rem;
  }

  .success-container {
    padding: 1rem;
  }

  .success-title {
    font-size: 1.75rem;
  }

  .success-message {
    font-size: 1rem;
  }
}
