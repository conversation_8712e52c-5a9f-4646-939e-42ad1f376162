// AuthLayout.jsx
import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';
import { FiShield, FiUsers } from 'react-icons/fi';
import Login from '../../components/Auth/Login';
import Register from '../../components/Auth/Register';
import Success from '../../components/Auth/Success';
import '../../assets/css/Custom.css';

const AuthLayout = () => {
  const [activeTab, setActiveTab] = useState('login'); // 'login', 'register', 'success'
  const containerRef = useRef(null);
  const formRef = useRef(null);
  const centerRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (formRef.current) {
      gsap.fromTo(formRef.current,
        { opacity: 0, x: activeTab === 'login' ? -50 : 50 },
        { opacity: 1, x: 0, duration: 0.6, ease: 'power2.out' }
      );
    }
  }, [activeTab]);

  const handleTabChange = (tab) => {
    if (tab === activeTab) return;

    gsap.to(formRef.current, {
      opacity: 0,
      x: tab === 'login' ? 50 : -50,
      duration: 0.3,
      onComplete: () => {
        setActiveTab(tab);
      },
    });
  };

  const handleSuccess = () => {
    gsap.to([formRef.current, centerRef.current], {
      opacity: 0,
      scale: 0.9,
      duration: 0.5,
      onComplete: () => setActiveTab('success'),
    });
  };

  const handleContinue = () => {
    navigate('/home');
  };

  return (
    <div className="compact-auth-container" ref={containerRef}>
      {/* Background Video */}
      <div className="video-background">
        <video autoPlay muted loop className="bg-video">
          <source src="https://player.vimeo.com/external/371433846.sd.mp4?s=236da2f3c0fd273d2c6d9a064f3ae35579b2bbdf&profile_id=139&oauth2_token_id=57447761" type="video/mp4" />
          <source src="https://assets.mixkit.co/videos/preview/mixkit-set-of-plateaus-seen-from-the-heights-in-a-sunset-26070-large.mp4" type="video/mp4" />
          {/* Fallback gradient if video fails to load */}
        </video>
        <div className="video-overlay"></div>
      </div>

      {/* Centered Content Container */}
      <div className="auth-content-wrapper">
        {activeTab === 'success' ? (
          <div className="success-wrapper" ref={formRef}>
            <Success onContinue={handleContinue} />
          </div>
        ) : (
          <div className="auth-card">
            {/* Logo Section */}
            <div className="auth-header">
              <div className="logo-section">
                <div className="logo-icon">
                  <FiShield size={28} />
                </div>
                <h3 className="logo-text">VMS</h3>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="tab-navigation">
              <button
                className={`tab-btn ${activeTab === 'login' ? 'active' : ''}`}
                onClick={() => handleTabChange('login')}
              >
                <FiShield size={18} />
                <span>Sign In</span>
              </button>
              <button
                className={`tab-btn ${activeTab === 'register' ? 'active' : ''}`}
                onClick={() => handleTabChange('register')}
              >
                <FiUsers size={18} />
                <span>Sign Up</span>
              </button>
            </div>

            {/* Center Illustration */}
            <div className="compact-illustration" ref={centerRef}>
              <div className="illustration-content">
                <h2 className="illustration-title">Start Your Journey.</h2>
                <p className="illustration-subtitle">
                  Join thousands of users who trust our platform
                </p>

                <div className="mini-graphic">
                  <div className="mini-envelope">
                    <div className="envelope-icon">
                      <FiShield size={20} />
                    </div>
                  </div>
                  <div className="floating-dots">
                    <div className="dot dot-1"></div>
                    <div className="dot dot-2"></div>
                    <div className="dot dot-3"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Form Section */}
            <div className="compact-form-section">
              <div className="form-wrapper" ref={formRef}>
                {activeTab === 'login' && <Login onSuccess={handleSuccess} />}
                {activeTab === 'register' && <Register onSuccess={handleSuccess} />}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthLayout;
