// AuthLayout.jsx
import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';
import Login from '../../components/Auth/Login';
import Register from '../../components/Auth/Register';
import Success from '../../components/Auth/Success';
import '../../assets/css/Custom.css';

const AuthLayout = () => {
  const [activeTab, setActiveTab] = useState('login'); // 'login', 'register', 'success'
  const containerRef = useRef(null);
  const formRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (formRef.current) {
      gsap.fromTo(formRef.current,
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.6, ease: 'power2.out' }
      );
    }
  }, [activeTab]);

  const handleTabChange = (tab) => {
    if (tab === activeTab) return;

    gsap.to(formRef.current, {
      opacity: 0,
      y: -20,
      duration: 0.3,
      onComplete: () => {
        setActiveTab(tab);
      },
    });
  };

  const handleSuccess = () => {
    gsap.to(formRef.current, {
      opacity: 0,
      scale: 0.9,
      duration: 0.5,
      onComplete: () => setActiveTab('success'),
    });
  };

  const handleContinue = () => {
    navigate('/home');
  };

  return (
    <div className="modern-signin-container" ref={containerRef}>
      {/* Left Side - Gradient Background */}
      <div className="signin-left-panel">
        <div className="signin-tabs-container">
          <div className="signin-tabs">
            <div
              className={`signin-tab ${activeTab === 'login' ? 'active' : ''}`}
              onClick={() => handleTabChange('login')}
            >
              LOGIN
            </div>
            <div
              className={`signin-tab ${activeTab === 'register' ? 'active' : ''}`}
              onClick={() => handleTabChange('register')}
            >
              SIGN IN
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Form */}
      <div className="signin-right-panel">
        {activeTab === 'success' ? (
          <div className="success-wrapper" ref={formRef}>
            <Success onContinue={handleContinue} />
          </div>
        ) : (
          <div className="signin-form-wrapper">
            {/* Logo Section */}
            <div className="signin-logo-section">
              <div className="signin-logo">
                <div className="logo-squares">
                  <div className="square square-1"></div>
                  <div className="square square-2"></div>
                  <div className="square square-3"></div>
                  <div className="square square-4"></div>
                </div>
              </div>
              <h1 className="signin-title">LOGIN</h1>
            </div>

            {/* Form Section */}
            <div className="signin-form-section" ref={formRef}>
              {activeTab === 'login' && <Login onSuccess={handleSuccess} />}
              {activeTab === 'register' && <Register onSuccess={handleSuccess} />}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthLayout;
